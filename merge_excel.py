#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件合并脚本
将vins(zh).xlsx的内容按ID顺序插入到text.xlsx中
"""

import pandas as pd
import os

def merge_excel_files():
    """
    合并两个Excel文件，按ID排序
    """
    try:
        # 检查文件是否存在
        if not os.path.exists('text.xlsx'):
            print("错误: text.xlsx 文件不存在")
            return False
        
        if not os.path.exists('vins(zh).xlsx'):
            print("错误: vins(zh).xlsx 文件不存在")
            return False
        
        # 读取text.xlsx文件
        print("正在读取 text.xlsx...")
        text_df = pd.read_excel('text.xlsx')
        print(f"text.xlsx 包含 {len(text_df)} 行数据")
        
        # 读取vins(zh).xlsx文件
        print("正在读取 vins(zh).xlsx...")
        vins_df = pd.read_excel('vins(zh).xlsx')
        print(f"vins(zh).xlsx 包含 {len(vins_df)} 行数据")
        
        # 检查列名，确保都有ID和文本列
        print(f"text.xlsx 列名: {list(text_df.columns)}")
        print(f"vins(zh).xlsx 列名: {list(vins_df.columns)}")
        
        # 假设第一列是ID，第二列是文本
        # 重命名列以确保一致性
        text_df.columns = ['ID', 'Text']
        vins_df.columns = ['ID', 'Text']
        
        # 确保ID列是数值类型
        text_df['ID'] = pd.to_numeric(text_df['ID'], errors='coerce')
        vins_df['ID'] = pd.to_numeric(vins_df['ID'], errors='coerce')
        
        # 删除ID为NaN的行
        text_df = text_df.dropna(subset=['ID'])
        vins_df = vins_df.dropna(subset=['ID'])
        
        # 合并数据框
        print("正在合并数据...")
        merged_df = pd.concat([text_df, vins_df], ignore_index=True)
        
        # 按ID排序
        print("正在按ID排序...")
        merged_df = merged_df.sort_values('ID').reset_index(drop=True)
        
        # 去除重复的ID（如果有的话），保留第一个
        print("正在处理重复ID...")
        original_count = len(merged_df)
        merged_df = merged_df.drop_duplicates(subset=['ID'], keep='first')
        if len(merged_df) < original_count:
            print(f"删除了 {original_count - len(merged_df)} 个重复的ID")
        
        # 备份原始text.xlsx文件
        print("正在备份原始文件...")
        if os.path.exists('text_backup.xlsx'):
            os.remove('text_backup.xlsx')
        os.rename('text.xlsx', 'text_backup.xlsx')
        
        # 保存合并后的数据到text.xlsx
        print("正在保存合并后的数据到 text.xlsx...")
        merged_df.to_excel('text.xlsx', index=False)
        
        print(f"合并完成！")
        print(f"最终文件包含 {len(merged_df)} 行数据")
        print(f"ID范围: {merged_df['ID'].min()} - {merged_df['ID'].max()}")
        print(f"原始文件已备份为 text_backup.xlsx")
        
        return True
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始合并Excel文件...")
    success = merge_excel_files()
    if success:
        print("操作成功完成！")
    else:
        print("操作失败！")
